---
import { readFileSync } from 'fs';
import { join } from 'path';

// Read navigation data
const contentPath = join(process.cwd(), 'data', 'content.json');
const contentData = JSON.parse(readFileSync(contentPath, 'utf-8'));
const navigation = contentData.navigation;
---

<nav class="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 w-full max-w-4xl px-4">
  <div class="glass-nav flex items-center justify-between">
    <a href="/" class="text-xl font-bold text-white hover:text-primary-200 transition-colors">
      Pediatric POCUS
    </a>
    
    <!-- Desktop Navigation -->
    <div class="hidden md:flex items-center space-x-6">
      {navigation.map((item: any) => (
        <a 
          href={item.href} 
          class="text-white/90 hover:text-white font-medium transition-colors duration-300 hover:scale-105 transform"
        >
          {item.name}
        </a>
      ))}
      <a href="/admin-panel" class="btn-secondary text-sm">
        Admin
      </a>
    </div>
    
    <!-- Mobile Menu Button -->
    <button 
      id="mobile-menu-button"
      class="md:hidden text-white p-2"
      aria-label="Toggle menu"
    >
      <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
      </svg>
    </button>
  </div>
  
  <!-- Mobile Navigation -->
  <div id="mobile-menu" class="hidden md:hidden mt-2">
    <div class="glass-card">
      {navigation.map((item: any) => (
        <a 
          href={item.href} 
          class="block text-white/90 hover:text-white font-medium py-3 px-4 transition-colors border-b border-white/10 last:border-b-0"
        >
          {item.name}
        </a>
      ))}
      <a href="/admin-panel" class="block text-white/90 hover:text-white font-medium py-3 px-4 transition-colors">
        Admin Panel
      </a>
    </div>
  </div>
</nav>

<script>
  // Mobile menu toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');
  
  mobileMenuButton?.addEventListener('click', () => {
    mobileMenu?.classList.toggle('hidden');
  });
  
  // Close mobile menu when clicking outside
  document.addEventListener('click', (e) => {
    if (!mobileMenuButton?.contains(e.target as Node) && !mobileMenu?.contains(e.target as Node)) {
      mobileMenu?.classList.add('hidden');
    }
  });
  
  // Close mobile menu when navigating
  const mobileLinks = mobileMenu?.querySelectorAll('a');
  mobileLinks?.forEach(link => {
    link.addEventListener('click', () => {
      mobileMenu?.classList.add('hidden');
    });
  });
</script>
