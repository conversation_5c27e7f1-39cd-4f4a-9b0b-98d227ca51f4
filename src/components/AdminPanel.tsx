import React, { useState, useEffect } from 'react';

interface Member {
  id: number;
  name: string;
  position: string;
  specialty: string;
  institution: string;
  bio: string;
  email: string;
  image: string;
  linkedin?: string;
}

interface Project {
  id: number;
  title: string;
  location: string;
  status: string;
  leadInvestigator: string;
  startDate: string;
  endDate: string;
  description: string;
  participants: number;
  budget: string;
  outcomes: string;
}

interface Event {
  id: number;
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  registrationLink: string;
}

interface ResearchProject {
  id: number;
  title: string;
  principalInvestigator: string;
  status: string;
  startDate: string;
  endDate: string;
  funding: string;
  fundingSource: string;
  description: string;
  objectives: string[];
  currentPhase: string;
  participants: number;
  publications: number;
}

interface EducationData {
  fellowshipPrograms: any[];
  trainingResources: any[];
  certificationInfo: any;
  learningResources: any;
}

interface GallerySection {
  id: number;
  title: string;
  year: string;
  description: string;
  photos: Array<{
    url: string;
    caption: string;
    alt: string;
  }>;
}

const AdminPanel: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('content');
  const [members, setMembers] = useState<Member[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [researchProjects, setResearchProjects] = useState<ResearchProject[]>([]);
  const [educationData, setEducationData] = useState<EducationData | null>(null);
  const [galleryData, setGalleryData] = useState<any>({});
  const [siteContent, setSiteContent] = useState<any>({});

  // Authentication state
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [loginError, setLoginError] = useState('');

  // Editing states
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [editingResearchProject, setEditingResearchProject] = useState<ResearchProject | null>(null);
  const [showAddMember, setShowAddMember] = useState(false);
  const [showAddProject, setShowAddProject] = useState(false);
  const [showAddEvent, setShowAddEvent] = useState(false);
  const [showAddResearchProject, setShowAddResearchProject] = useState(false);

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth');
      const data = await response.json();
      setIsAuthenticated(data.authenticated);
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');
    
    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginForm),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setIsAuthenticated(true);
        loadData();
      } else {
        setLoginError(data.message);
      }
    } catch (error) {
      setLoginError('Login failed. Please try again.');
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth', { method: 'DELETE' });
      setIsAuthenticated(false);
      setLoginForm({ username: '', password: '' });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const loadData = async () => {
    try {
      // Load members
      const membersResponse = await fetch('/api/members');
      const membersData = await membersResponse.json();
      setMembers(membersData.boardMembers || []);

      // Load projects
      const projectsResponse = await fetch('/api/projects');
      const projectsData = await projectsResponse.json();
      setProjects(projectsData.globalHealthProjects || []);

      // Load research projects
      const researchResponse = await fetch('/api/research');
      const researchData = await researchResponse.json();
      setResearchProjects(researchData.currentProjects || []);

      // Load education data
      const educationResponse = await fetch('/api/education');
      const educationData = await educationResponse.json();
      setEducationData(educationData);

      // Load gallery data
      const galleryResponse = await fetch('/api/gallery');
      const galleryData = await galleryResponse.json();
      setGalleryData(galleryData);

      // Load content
      const contentResponse = await fetch('/api/content');
      const contentData = await contentResponse.json();
      setSiteContent(contentData);
      setEvents(contentData.site?.upcomingEvents || []);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadData();
    }
  }, [isAuthenticated]);

  // CRUD Functions for Members
  const saveMember = async (member: Partial<Member>) => {
    try {
      const method = member.id ? 'PUT' : 'POST';
      const response = await fetch('/api/members', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(member),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        setEditingMember(null);
        setShowAddMember(false);
      } else {
        alert('Error saving member: ' + data.message);
      }
    } catch (error) {
      alert('Error saving member');
    }
  };

  const deleteMember = async (id: number) => {
    if (!confirm('Are you sure you want to delete this member?')) return;

    try {
      const response = await fetch(`/api/members?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
      } else {
        alert('Error deleting member: ' + data.message);
      }
    } catch (error) {
      alert('Error deleting member');
    }
  };

  // CRUD Functions for Projects
  const saveProject = async (project: Partial<Project>) => {
    try {
      const method = project.id ? 'PUT' : 'POST';
      const response = await fetch('/api/projects', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(project),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        setEditingProject(null);
        setShowAddProject(false);
      } else {
        alert('Error saving project: ' + data.message);
      }
    } catch (error) {
      alert('Error saving project');
    }
  };

  const deleteProject = async (id: number) => {
    if (!confirm('Are you sure you want to delete this project?')) return;

    try {
      const response = await fetch(`/api/projects?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
      } else {
        alert('Error deleting project: ' + data.message);
      }
    } catch (error) {
      alert('Error deleting project');
    }
  };

  // CRUD Functions for Events
  const saveEvent = async (event: Partial<Event>) => {
    try {
      const updatedContent = { ...siteContent };
      if (!updatedContent.site) updatedContent.site = {};
      if (!updatedContent.site.upcomingEvents) updatedContent.site.upcomingEvents = [];

      if (event.id) {
        // Update existing event
        const eventIndex = updatedContent.site.upcomingEvents.findIndex((e: Event) => e.id === event.id);
        if (eventIndex !== -1) {
          updatedContent.site.upcomingEvents[eventIndex] = event;
        }
      } else {
        // Add new event
        const newId = Math.max(0, ...updatedContent.site.upcomingEvents.map((e: Event) => e.id)) + 1;
        updatedContent.site.upcomingEvents.push({ ...event, id: newId });
      }

      const response = await fetch('/api/content', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedContent),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        setEditingEvent(null);
        setShowAddEvent(false);
      } else {
        alert('Error saving event: ' + data.message);
      }
    } catch (error) {
      alert('Error saving event');
    }
  };

  const deleteEvent = async (id: number) => {
    if (!confirm('Are you sure you want to delete this event?')) return;

    try {
      const updatedContent = { ...siteContent };
      if (updatedContent.site?.upcomingEvents) {
        updatedContent.site.upcomingEvents = updatedContent.site.upcomingEvents.filter((e: Event) => e.id !== id);
      }

      const response = await fetch('/api/content', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedContent),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
      } else {
        alert('Error deleting event: ' + data.message);
      }
    } catch (error) {
      alert('Error deleting event');
    }
  };

  // CRUD Functions for Research Projects
  const saveResearchProject = async (project: Partial<ResearchProject>) => {
    try {
      const method = project.id ? 'PUT' : 'POST';
      const response = await fetch('/api/research', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(project),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        setEditingResearchProject(null);
        setShowAddResearchProject(false);
      } else {
        alert('Error saving research project: ' + data.message);
      }
    } catch (error) {
      alert('Error saving research project');
    }
  };

  const deleteResearchProject = async (id: number) => {
    if (!confirm('Are you sure you want to delete this research project?')) return;

    try {
      const response = await fetch(`/api/research?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
      } else {
        alert('Error deleting research project: ' + data.message);
      }
    } catch (error) {
      alert('Error deleting research project');
    }
  };

  // Content Management Functions
  const saveContent = async (updatedContent: any) => {
    try {
      const response = await fetch('/api/content', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedContent),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        alert('Content updated successfully!');
      } else {
        alert('Error updating content: ' + data.message);
      }
    } catch (error) {
      alert('Error updating content');
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="glass-card p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
          <p className="text-white mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '80vh',
        padding: '20px'
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255,255,255,0.2)',
          borderRadius: '16px',
          padding: '32px',
          maxWidth: '400px',
          width: '100%',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
        }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'white',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            Admin Login
          </h2>
          <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{
                color: 'white',
                fontWeight: '500',
                marginBottom: '8px',
                display: 'block'
              }}>
                Username
              </label>
              <input
                type="text"
                value={loginForm.username}
                onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
                placeholder="Enter username"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '16px'
                }}
              />
            </div>
            <div>
              <label style={{
                color: 'white',
                fontWeight: '500',
                marginBottom: '8px',
                display: 'block'
              }}>
                Password
              </label>
              <input
                type="password"
                value={loginForm.password}
                onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                placeholder="Enter password"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '16px'
                }}
              />
            </div>
            {loginError && (
              <div style={{
                background: 'rgba(239, 68, 68, 0.2)',
                color: '#fca5a5',
                padding: '12px',
                borderRadius: '8px',
                fontSize: '14px'
              }}>
                {loginError}
              </div>
            )}
            <button
              type="submit"
              style={{
                width: '100%',
                padding: '12px 24px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                fontWeight: '600',
                fontSize: '16px',
                cursor: 'pointer',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
                transition: 'transform 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
              onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
            >
              Login
            </button>
          </form>
          <div style={{
            marginTop: '24px',
            padding: '16px',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '8px'
          }}>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '14px',
              textAlign: 'center',
              lineHeight: '1.5'
            }}>
              Demo Credentials:<br />
              Username: <span style={{ color: 'white', fontFamily: 'monospace' }}>admin</span><br />
              Password: <span style={{ color: 'white', fontFamily: 'monospace' }}>password</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '32px 16px' }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        marginBottom: '32px',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            color: 'white',
            margin: 0
          }}>
            Admin Panel
          </h1>
          <button
            onClick={handleLogout}
            style={{
              padding: '8px 16px',
              background: 'rgba(255,255,255,0.1)',
              border: '1px solid rgba(255,255,255,0.2)',
              borderRadius: '20px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Logout
          </button>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          gap: '16px',
          marginBottom: '32px',
          overflowX: 'auto',
          paddingBottom: '8px'
        }}>
          {[
            { id: 'content', label: 'Site Content' },
            { id: 'members', label: 'Board Members' },
            { id: 'projects', label: 'Global Health Projects' },
            { id: 'events', label: 'Events' },
            { id: 'research', label: 'Research' },
            { id: 'education', label: 'Education' },
            { id: 'gallery', label: 'Photo Gallery' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                padding: '8px 16px',
                borderRadius: '8px',
                fontWeight: '500',
                whiteSpace: 'nowrap',
                border: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s',
                background: activeTab === tab.id ? '#667eea' : 'transparent',
                color: activeTab === tab.id ? 'white' : 'rgba(255,255,255,0.7)'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div style={{ minHeight: '400px' }}>
          {activeTab === 'content' && <ContentManager content={siteContent} onSave={saveContent} />}
          {activeTab === 'members' && (
            <MembersManager
              members={members}
              onEdit={setEditingMember}
              onDelete={deleteMember}
              onAdd={() => setShowAddMember(true)}
            />
          )}
          {activeTab === 'projects' && (
            <ProjectsManager
              projects={projects}
              onEdit={setEditingProject}
              onDelete={deleteProject}
              onAdd={() => setShowAddProject(true)}
            />
          )}
          {activeTab === 'events' && (
            <EventsManager
              events={events}
              onEdit={setEditingEvent}
              onDelete={deleteEvent}
              onAdd={() => setShowAddEvent(true)}
            />
          )}
          {activeTab === 'research' && (
            <ResearchManager
              projects={researchProjects}
              onEdit={setEditingResearchProject}
              onDelete={deleteResearchProject}
              onAdd={() => setShowAddResearchProject(true)}
            />
          )}
          {activeTab === 'education' && educationData && (
            <EducationManager
              data={educationData}
              onSave={async (updatedData) => {
                try {
                  const response = await fetch('/api/education', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updatedData),
                  });
                  const result = await response.json();
                  if (result.success) {
                    await loadData();
                    alert('Education data updated successfully!');
                  } else {
                    alert('Error updating education data: ' + result.message);
                  }
                } catch (error) {
                  alert('Error updating education data');
                }
              }}
            />
          )}
          {activeTab === 'gallery' && (
            <GalleryManager
              data={galleryData}
              onSave={async (updatedData) => {
                try {
                  const response = await fetch('/api/gallery', {
                    method: 'PUT',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(updatedData),
                  });
                  const result = await response.json();
                  if (result.success) {
                    await loadData();
                    alert('Gallery data updated successfully!');
                  } else {
                    alert('Error updating gallery data: ' + result.message);
                  }
                } catch (error) {
                  alert('Error updating gallery data');
                }
              }}
            />
          )}
        </div>

        {/* Modal Forms */}
        {(editingMember || showAddMember) && (
          <MemberForm
            member={editingMember}
            onSave={saveMember}
            onCancel={() => {
              setEditingMember(null);
              setShowAddMember(false);
            }}
          />
        )}

        {(editingProject || showAddProject) && (
          <ProjectForm
            project={editingProject}
            onSave={saveProject}
            onCancel={() => {
              setEditingProject(null);
              setShowAddProject(false);
            }}
          />
        )}

        {(editingEvent || showAddEvent) && (
          <EventForm
            event={editingEvent}
            onSave={saveEvent}
            onCancel={() => {
              setEditingEvent(null);
              setShowAddEvent(false);
            }}
          />
        )}

        {(editingResearchProject || showAddResearchProject) && (
          <ResearchProjectForm
            project={editingResearchProject}
            onSave={saveResearchProject}
            onCancel={() => {
              setEditingResearchProject(null);
              setShowAddResearchProject(false);
            }}
          />
        )}
      </div>
    </div>
  );
};

// Content Manager Component
const ContentManager: React.FC<{ content: any; onSave: (content: any) => void }> = ({ content, onSave }) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});

  const startEditing = (section: string) => {
    setEditingSection(section);
    if (section === 'hero') {
      setFormData({
        title: content.site?.hero?.title || '',
        subtitle: content.site?.hero?.subtitle || '',
        highlights: content.site?.hero?.highlights?.join('\n') || ''
      });
    } else if (section === 'site') {
      setFormData({
        title: content.site?.title || '',
        subtitle: content.site?.subtitle || '',
        description: content.site?.description || ''
      });
    } else if (section === 'navigation') {
      setFormData({
        navigation: JSON.stringify(content.navigation || [], null, 2)
      });
    } else if (section === 'adminResources') {
      setFormData({
        title: content.adminResources?.title || '',
        description: content.adminResources?.description || '',
        links: JSON.stringify(content.adminResources?.links || [], null, 2)
      });
    }
  };

  const saveSection = () => {
    const updatedContent = { ...content };

    if (editingSection === 'hero') {
      if (!updatedContent.site) updatedContent.site = {};
      if (!updatedContent.site.hero) updatedContent.site.hero = {};
      updatedContent.site.hero.title = formData.title;
      updatedContent.site.hero.subtitle = formData.subtitle;
      updatedContent.site.hero.highlights = formData.highlights.split('\n').filter((h: string) => h.trim());
    } else if (editingSection === 'site') {
      if (!updatedContent.site) updatedContent.site = {};
      updatedContent.site.title = formData.title;
      updatedContent.site.subtitle = formData.subtitle;
      updatedContent.site.description = formData.description;
    } else if (editingSection === 'navigation') {
      try {
        updatedContent.navigation = JSON.parse(formData.navigation);
      } catch (e) {
        alert('Invalid JSON format for navigation');
        return;
      }
    } else if (editingSection === 'adminResources') {
      if (!updatedContent.adminResources) updatedContent.adminResources = {};
      updatedContent.adminResources.title = formData.title;
      updatedContent.adminResources.description = formData.description;
      try {
        updatedContent.adminResources.links = JSON.parse(formData.links);
      } catch (e) {
        alert('Invalid JSON format for admin resource links');
        return;
      }
    }

    onSave(updatedContent);
    setEditingSection(null);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
        Site Content Management
      </h2>

      {/* Hero Section */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Hero Section</h3>
          <button
            onClick={() => startEditing('hero')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <div style={{ color: 'rgba(255,255,255,0.8)' }}>
          <p><strong>Title:</strong> {content.site?.hero?.title}</p>
          <p><strong>Subtitle:</strong> {content.site?.hero?.subtitle}</p>
          <p><strong>Highlights:</strong> {content.site?.hero?.highlights?.join(', ')}</p>
        </div>
      </div>

      {/* Site Information */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Site Information</h3>
          <button
            onClick={() => startEditing('site')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <div style={{ color: 'rgba(255,255,255,0.8)' }}>
          <p><strong>Title:</strong> {content.site?.title}</p>
          <p><strong>Subtitle:</strong> {content.site?.subtitle}</p>
          <p><strong>Description:</strong> {content.site?.description}</p>
        </div>
      </div>

      {/* Navigation */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Navigation Links</h3>
          <button
            onClick={() => startEditing('navigation')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <div style={{ color: 'rgba(255,255,255,0.8)' }}>
          {content.navigation?.map((nav: any, index: number) => (
            <p key={index}><strong>{nav.name}:</strong> {nav.href}</p>
          ))}
        </div>
      </div>

      {/* Admin Resources */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Admin Resources</h3>
          <button
            onClick={() => startEditing('adminResources')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <div style={{ color: 'rgba(255,255,255,0.8)' }}>
          <p><strong>Title:</strong> {content.adminResources?.title}</p>
          <p><strong>Description:</strong> {content.adminResources?.description}</p>
          <p><strong>Links:</strong> {content.adminResources?.links?.length || 0} items</p>
        </div>
      </div>

      {/* Edit Modal */}
      {editingSection && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '16px',
            padding: '32px',
            maxWidth: '600px',
            width: '100%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
              Edit {editingSection === 'hero' ? 'Hero Section' :
                   editingSection === 'site' ? 'Site Information' :
                   editingSection === 'navigation' ? 'Navigation' : 'Admin Resources'}
            </h2>

            <form onSubmit={(e) => { e.preventDefault(); saveSection(); }} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {editingSection === 'hero' && (
                <>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Subtitle</label>
                    <input
                      type="text"
                      value={formData.subtitle}
                      onChange={(e) => setFormData({ ...formData, subtitle: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Highlights (one per line)</label>
                    <textarea
                      value={formData.highlights}
                      onChange={(e) => setFormData({ ...formData, highlights: e.target.value })}
                      rows={4}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </>
              )}

              {editingSection === 'site' && (
                <>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Site Title</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Subtitle</label>
                    <input
                      type="text"
                      value={formData.subtitle}
                      onChange={(e) => setFormData({ ...formData, subtitle: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </>
              )}

              {editingSection === 'navigation' && (
                <div>
                  <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Navigation JSON</label>
                  <textarea
                    value={formData.navigation}
                    onChange={(e) => setFormData({ ...formData, navigation: e.target.value })}
                    rows={10}
                    style={{
                      width: '100%',
                      padding: '12px',
                      background: 'rgba(255,255,255,0.1)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white',
                      fontFamily: 'monospace',
                      fontSize: '14px',
                      resize: 'vertical'
                    }}
                  />
                </div>
              )}

              {editingSection === 'adminResources' && (
                <>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Links JSON</label>
                    <textarea
                      value={formData.links}
                      onChange={(e) => setFormData({ ...formData, links: e.target.value })}
                      rows={8}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        fontFamily: 'monospace',
                        fontSize: '14px',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </>
              )}

              <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
                <button
                  type="submit"
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  Save Changes
                </button>
                <button
                  type="button"
                  onClick={() => setEditingSection(null)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    color: 'white',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

// Members Manager Component
const MembersManager: React.FC<{
  members: Member[];
  onEdit: (member: Member) => void;
  onDelete: (id: number) => void;
  onAdd: () => void;
}> = ({ members, onEdit, onDelete, onAdd }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Board Members Management
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Member
        </button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px'
      }}>
        {members.map((member) => (
          <div key={member.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: 'white',
              marginBottom: '8px'
            }}>
              {member.name}
            </h3>
            <p style={{
              color: '#a5b8ff',
              fontSize: '14px',
              marginBottom: '8px'
            }}>
              {member.position}
            </p>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '14px',
              marginBottom: '8px'
            }}>
              {member.specialty}
            </p>
            <p style={{
              color: 'rgba(255,255,255,0.6)',
              fontSize: '14px',
              marginBottom: '16px'
            }}>
              {member.institution}
            </p>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={() => onEdit(member)}
                style={{
                  padding: '4px 12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '4px',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                Edit
              </button>
              <button
                onClick={() => onDelete(member.id)}
                style={{
                  padding: '4px 12px',
                  background: 'rgba(239, 68, 68, 0.2)',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  borderRadius: '4px',
                  color: '#fca5a5',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Projects Manager Component
const ProjectsManager: React.FC<{
  projects: Project[];
  onEdit: (project: Project) => void;
  onDelete: (id: number) => void;
  onAdd: () => void;
}> = ({ projects, onEdit, onDelete, onAdd }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
      case 'planning': return { bg: 'rgba(234, 179, 8, 0.2)', color: '#fde047' };
      case 'completed': return { bg: 'rgba(59, 130, 246, 0.2)', color: '#93c5fd' };
      default: return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Global Health Projects
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Project
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {projects.map((project) => (
          <div key={project.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {project.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '16px',
                  fontSize: '14px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Location</p>
                    <p style={{ color: 'white' }}>{project.location}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Status</p>
                    <span style={{
                      ...getStatusColor(project.status),
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500'
                    }}>
                      {project.status}
                    </span>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Lead</p>
                    <p style={{ color: 'white' }}>{project.leadInvestigator}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Budget</p>
                    <p style={{ color: '#a5b8ff' }}>{project.budget}</p>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => onEdit(project)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '4px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Edit
                </button>
                <button
                  onClick={() => onDelete(project.id)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(239, 68, 68, 0.2)',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '4px',
                    color: '#fca5a5',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Events Manager Component
const EventsManager: React.FC<{
  events: Event[];
  onEdit: (event: Event) => void;
  onDelete: (id: number) => void;
  onAdd: () => void;
}> = ({ events, onEdit, onDelete, onAdd }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Upcoming Events
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Event
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {events.map((event) => (
          <div key={event.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {event.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '16px',
                  fontSize: '14px',
                  marginBottom: '12px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Date & Time</p>
                    <p style={{ color: 'white' }}>
                      {new Date(event.date).toLocaleDateString()} at {event.time}
                    </p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Location</p>
                    <p style={{ color: 'white' }}>{event.location}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Registration</p>
                    <a
                      href={event.registrationLink}
                      style={{ color: '#a5b8ff', textDecoration: 'none' }}
                      onMouseOver={(e) => e.currentTarget.style.color = '#c7d6ff'}
                      onMouseOut={(e) => e.currentTarget.style.color = '#a5b8ff'}
                    >
                      View Link
                    </a>
                  </div>
                </div>
                <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
                  {event.description}
                </p>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => onEdit(event)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '4px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Edit
                </button>
                <button
                  onClick={() => onDelete(event.id)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(239, 68, 68, 0.2)',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '4px',
                    color: '#fca5a5',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Member Form Component
const MemberForm: React.FC<{
  member: Member | null;
  onSave: (member: Partial<Member>) => void;
  onCancel: () => void;
}> = ({ member, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: member?.name || '',
    position: member?.position || '',
    specialty: member?.specialty || '',
    institution: member?.institution || '',
    bio: member?.bio || '',
    email: member?.email || '',
    image: member?.image || '/images/board/placeholder.jpg',
    linkedin: member?.linkedin || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(member ? { ...formData, id: member.id } : formData);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '500px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
          {member ? 'Edit Member' : 'Add New Member'}
        </h2>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Position</label>
            <input
              type="text"
              value={formData.position}
              onChange={(e) => setFormData({ ...formData, position: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Specialty</label>
            <input
              type="text"
              value={formData.specialty}
              onChange={(e) => setFormData({ ...formData, specialty: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Institution</label>
            <input
              type="text"
              value={formData.institution}
              onChange={(e) => setFormData({ ...formData, institution: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>LinkedIn Profile URL</label>
            <input
              type="url"
              value={formData.linkedin}
              onChange={(e) => setFormData({ ...formData, linkedin: e.target.value })}
              placeholder="https://linkedin.com/in/username"
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Profile Image URL</label>
            <input
              type="url"
              value={formData.image}
              onChange={(e) => setFormData({ ...formData, image: e.target.value })}
              placeholder="https://example.com/image.jpg"
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Bio</label>
            <textarea
              value={formData.bio}
              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
              required
              rows={4}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              type="submit"
              style={{
                flex: 1,
                padding: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              {member ? 'Update' : 'Add'} Member
            </button>
            <button
              type="button"
              onClick={onCancel}
              style={{
                flex: 1,
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Project Form Component
const ProjectForm: React.FC<{
  project: Project | null;
  onSave: (project: Partial<Project>) => void;
  onCancel: () => void;
}> = ({ project, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: project?.title || '',
    location: project?.location || '',
    status: project?.status || 'Planning',
    leadInvestigator: project?.leadInvestigator || '',
    startDate: project?.startDate || '',
    endDate: project?.endDate || '',
    description: project?.description || '',
    participants: project?.participants || 0,
    budget: project?.budget || '',
    outcomes: project?.outcomes || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(project ? { ...formData, id: project.id } : formData);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
          {project ? 'Edit Project' : 'Add New Project'}
        </h2>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Location</label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              >
                <option value="Planning">Planning</option>
                <option value="Active">Active</option>
                <option value="Completed">Completed</option>
              </select>
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Lead Investigator</label>
              <input
                type="text"
                value={formData.leadInvestigator}
                onChange={(e) => setFormData({ ...formData, leadInvestigator: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Start Date</label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>End Date</label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Participants</label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => setFormData({ ...formData, participants: parseInt(e.target.value) || 0 })}
                min="0"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Budget</label>
              <input
                type="text"
                value={formData.budget}
                onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                placeholder="e.g., $125,000"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              required
              rows={3}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Outcomes</label>
            <textarea
              value={formData.outcomes}
              onChange={(e) => setFormData({ ...formData, outcomes: e.target.value })}
              rows={3}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              type="submit"
              style={{
                flex: 1,
                padding: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              {project ? 'Update' : 'Add'} Project
            </button>
            <button
              type="button"
              onClick={onCancel}
              style={{
                flex: 1,
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Research Manager Component
const ResearchManager: React.FC<{
  projects: ResearchProject[];
  onEdit: (project: ResearchProject) => void;
  onDelete: (id: number) => void;
  onAdd: () => void;
}> = ({ projects, onEdit, onDelete, onAdd }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
      case 'planning': return { bg: 'rgba(234, 179, 8, 0.2)', color: '#fde047' };
      case 'completed': return { bg: 'rgba(59, 130, 246, 0.2)', color: '#93c5fd' };
      default: return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Research Projects
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Research Project
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {projects.map((project) => (
          <div key={project.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {project.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '16px',
                  fontSize: '14px',
                  marginBottom: '12px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Principal Investigator</p>
                    <p style={{ color: 'white' }}>{project.principalInvestigator}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Status</p>
                    <span style={{
                      ...getStatusColor(project.status),
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500'
                    }}>
                      {project.status}
                    </span>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Funding</p>
                    <p style={{ color: '#a5b8ff' }}>{project.funding}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Participants</p>
                    <p style={{ color: 'white' }}>{project.participants}</p>
                  </div>
                </div>
                <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginBottom: '8px' }}>
                  {project.description}
                </p>
                <p style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px' }}>
                  Phase: {project.currentPhase} | Publications: {project.publications}
                </p>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => onEdit(project)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '4px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Edit
                </button>
                <button
                  onClick={() => onDelete(project.id)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(239, 68, 68, 0.2)',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '4px',
                    color: '#fca5a5',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Event Form Component
const EventForm: React.FC<{
  event: Event | null;
  onSave: (event: Partial<Event>) => void;
  onCancel: () => void;
}> = ({ event, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: event?.title || '',
    date: event?.date || '',
    time: event?.time || '',
    location: event?.location || '',
    description: event?.description || '',
    registrationLink: event?.registrationLink || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(event ? { ...formData, id: event.id } : formData);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '500px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
          {event ? 'Edit Event' : 'Add New Event'}
        </h2>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Date</label>
              <input
                type="date"
                value={formData.date}
                onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Time</label>
              <input
                type="text"
                value={formData.time}
                onChange={(e) => setFormData({ ...formData, time: e.target.value })}
                placeholder="e.g., 2:00 PM EST"
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Location</label>
            <input
              type="text"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              required
              rows={3}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Registration Link</label>
            <input
              type="url"
              value={formData.registrationLink}
              onChange={(e) => setFormData({ ...formData, registrationLink: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              type="submit"
              style={{
                flex: 1,
                padding: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              {event ? 'Update' : 'Add'} Event
            </button>
            <button
              type="button"
              onClick={onCancel}
              style={{
                flex: 1,
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Research Project Form Component
const ResearchProjectForm: React.FC<{
  project: ResearchProject | null;
  onSave: (project: Partial<ResearchProject>) => void;
  onCancel: () => void;
}> = ({ project, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: project?.title || '',
    principalInvestigator: project?.principalInvestigator || '',
    status: project?.status || 'Planning',
    startDate: project?.startDate || '',
    endDate: project?.endDate || '',
    funding: project?.funding || '',
    fundingSource: project?.fundingSource || '',
    description: project?.description || '',
    objectives: project?.objectives?.join('\n') || '',
    currentPhase: project?.currentPhase || '',
    participants: project?.participants || 0,
    publications: project?.publications || 0
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const projectData = {
      ...formData,
      objectives: formData.objectives.split('\n').filter(obj => obj.trim())
    };
    onSave(project ? { ...projectData, id: project.id } : projectData);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '700px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
          {project ? 'Edit Research Project' : 'Add New Research Project'}
        </h2>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Principal Investigator</label>
              <input
                type="text"
                value={formData.principalInvestigator}
                onChange={(e) => setFormData({ ...formData, principalInvestigator: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              >
                <option value="Planning">Planning</option>
                <option value="Active">Active</option>
                <option value="Completed">Completed</option>
              </select>
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Start Date</label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>End Date</label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Funding</label>
              <input
                type="text"
                value={formData.funding}
                onChange={(e) => setFormData({ ...formData, funding: e.target.value })}
                placeholder="e.g., $450,000"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Funding Source</label>
              <input
                type="text"
                value={formData.fundingSource}
                onChange={(e) => setFormData({ ...formData, fundingSource: e.target.value })}
                placeholder="e.g., NIH R01"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              required
              rows={3}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Objectives (one per line)</label>
            <textarea
              value={formData.objectives}
              onChange={(e) => setFormData({ ...formData, objectives: e.target.value })}
              rows={4}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Current Phase</label>
              <input
                type="text"
                value={formData.currentPhase}
                onChange={(e) => setFormData({ ...formData, currentPhase: e.target.value })}
                placeholder="e.g., Data Collection"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Participants</label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => setFormData({ ...formData, participants: parseInt(e.target.value) || 0 })}
                min="0"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Publications</label>
              <input
                type="number"
                value={formData.publications}
                onChange={(e) => setFormData({ ...formData, publications: parseInt(e.target.value) || 0 })}
                min="0"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              type="submit"
              style={{
                flex: 1,
                padding: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              {project ? 'Update' : 'Add'} Research Project
            </button>
            <button
              type="button"
              onClick={onCancel}
              style={{
                flex: 1,
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Education Manager Component
const EducationManager: React.FC<{
  data: EducationData;
  onSave: (data: EducationData) => void;
}> = ({ data, onSave }) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [formData, setFormData] = useState<any>({});

  const startEditing = (section: string) => {
    setEditingSection(section);
    if (section === 'learningResources') {
      setFormData({
        title: data.learningResources?.title || '',
        description: data.learningResources?.description || '',
        pastWebinars: JSON.stringify(data.learningResources?.pastWebinars || [], null, 2),
        pastConferences: JSON.stringify(data.learningResources?.pastConferences || [], null, 2),
        landmarkPapers: JSON.stringify(data.learningResources?.landmarkPapers || [], null, 2)
      });
    } else if (section === 'fellowshipPrograms') {
      setFormData({
        fellowshipPrograms: JSON.stringify(data.fellowshipPrograms || [], null, 2)
      });
    } else if (section === 'trainingResources') {
      setFormData({
        trainingResources: JSON.stringify(data.trainingResources || [], null, 2)
      });
    } else if (section === 'certificationInfo') {
      setFormData({
        certificationInfo: JSON.stringify(data.certificationInfo || {}, null, 2)
      });
    }
  };

  const saveSection = () => {
    const updatedData = { ...data };

    try {
      if (editingSection === 'learningResources') {
        updatedData.learningResources = {
          title: formData.title,
          description: formData.description,
          pastWebinars: JSON.parse(formData.pastWebinars),
          pastConferences: JSON.parse(formData.pastConferences),
          landmarkPapers: JSON.parse(formData.landmarkPapers)
        };
      } else if (editingSection === 'fellowshipPrograms') {
        updatedData.fellowshipPrograms = JSON.parse(formData.fellowshipPrograms);
      } else if (editingSection === 'trainingResources') {
        updatedData.trainingResources = JSON.parse(formData.trainingResources);
      } else if (editingSection === 'certificationInfo') {
        updatedData.certificationInfo = JSON.parse(formData.certificationInfo);
      }

      onSave(updatedData);
      setEditingSection(null);
    } catch (e) {
      alert('Invalid JSON format. Please check your input.');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
        Education Management
      </h2>

      {/* Fellowship Programs */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Fellowship Programs</h3>
          <button
            onClick={() => startEditing('fellowshipPrograms')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <p style={{ color: 'rgba(255,255,255,0.8)' }}>
          {data.fellowshipPrograms?.length || 0} fellowship programs configured
        </p>
      </div>

      {/* Training Resources */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Training Resources</h3>
          <button
            onClick={() => startEditing('trainingResources')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <p style={{ color: 'rgba(255,255,255,0.8)' }}>
          {data.trainingResources?.length || 0} training resources available
        </p>
      </div>

      {/* Certification Info */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Certification Information</h3>
          <button
            onClick={() => startEditing('certificationInfo')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <p style={{ color: 'rgba(255,255,255,0.8)' }}>
          {data.certificationInfo?.levels?.length || 0} certification levels available
        </p>
      </div>

      {/* Learning Resources */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Learning Resources</h3>
          <button
            onClick={() => startEditing('learningResources')}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <div style={{ color: 'rgba(255,255,255,0.8)' }}>
          <p><strong>Past Webinars:</strong> {data.learningResources?.pastWebinars?.length || 0}</p>
          <p><strong>Past Conferences:</strong> {data.learningResources?.pastConferences?.length || 0}</p>
          <p><strong>Landmark Papers:</strong> {data.learningResources?.landmarkPapers?.length || 0}</p>
        </div>
      </div>

      {/* Edit Modal */}
      {editingSection && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '16px',
            padding: '32px',
            maxWidth: '800px',
            width: '100%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
              Edit {editingSection === 'learningResources' ? 'Learning Resources' :
                   editingSection === 'fellowshipPrograms' ? 'Fellowship Programs' :
                   editingSection === 'trainingResources' ? 'Training Resources' : 'Certification Info'}
            </h2>

            <form onSubmit={(e) => { e.preventDefault(); saveSection(); }} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              {editingSection === 'learningResources' && (
                <>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      rows={3}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Past Webinars JSON</label>
                    <textarea
                      value={formData.pastWebinars}
                      onChange={(e) => setFormData({ ...formData, pastWebinars: e.target.value })}
                      rows={6}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        fontFamily: 'monospace',
                        fontSize: '14px',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Past Conferences JSON</label>
                    <textarea
                      value={formData.pastConferences}
                      onChange={(e) => setFormData({ ...formData, pastConferences: e.target.value })}
                      rows={6}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        fontFamily: 'monospace',
                        fontSize: '14px',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                  <div>
                    <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Landmark Papers JSON</label>
                    <textarea
                      value={formData.landmarkPapers}
                      onChange={(e) => setFormData({ ...formData, landmarkPapers: e.target.value })}
                      rows={6}
                      style={{
                        width: '100%',
                        padding: '12px',
                        background: 'rgba(255,255,255,0.1)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px',
                        color: 'white',
                        fontFamily: 'monospace',
                        fontSize: '14px',
                        resize: 'vertical'
                      }}
                    />
                  </div>
                </>
              )}

              {(editingSection === 'fellowshipPrograms' || editingSection === 'trainingResources' || editingSection === 'certificationInfo') && (
                <div>
                  <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>
                    {editingSection === 'fellowshipPrograms' ? 'Fellowship Programs' :
                     editingSection === 'trainingResources' ? 'Training Resources' : 'Certification Info'} JSON
                  </label>
                  <textarea
                    value={editingSection === 'fellowshipPrograms' ? formData.fellowshipPrograms :
                           editingSection === 'trainingResources' ? formData.trainingResources : formData.certificationInfo}
                    onChange={(e) => setFormData({
                      ...formData,
                      [editingSection === 'fellowshipPrograms' ? 'fellowshipPrograms' :
                       editingSection === 'trainingResources' ? 'trainingResources' : 'certificationInfo']: e.target.value
                    })}
                    rows={15}
                    style={{
                      width: '100%',
                      padding: '12px',
                      background: 'rgba(255,255,255,0.1)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white',
                      fontFamily: 'monospace',
                      fontSize: '14px',
                      resize: 'vertical'
                    }}
                  />
                </div>
              )}

              <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
                <button
                  type="submit"
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  Save Changes
                </button>
                <button
                  type="button"
                  onClick={() => setEditingSection(null)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    color: 'white',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

// Gallery Manager Component
const GalleryManager: React.FC<{
  data: any;
  onSave: (data: any) => void;
}> = ({ data, onSave }) => {
  const [editingSection, setEditingSection] = useState<string | null>(null);
  const [editingGallerySection, setEditingGallerySection] = useState<GallerySection | null>(null);
  const [showAddSection, setShowAddSection] = useState(false);
  const [formData, setFormData] = useState<any>({});

  const startEditingHeader = () => {
    setEditingSection('header');
    setFormData({
      title: data.title || '',
      description: data.description || ''
    });
  };

  const startEditingGallerySection = (section: GallerySection | null) => {
    setEditingGallerySection(section);
    setFormData({
      title: section?.title || '',
      year: section?.year || '',
      description: section?.description || '',
      photos: JSON.stringify(section?.photos || [], null, 2)
    });
  };

  const saveHeader = () => {
    const updatedData = {
      ...data,
      title: formData.title,
      description: formData.description
    };
    onSave(updatedData);
    setEditingSection(null);
  };

  const saveGallerySection = async () => {
    try {
      const photos = JSON.parse(formData.photos);
      const sectionData = {
        title: formData.title,
        year: formData.year,
        description: formData.description,
        photos: photos
      };

      if (editingGallerySection) {
        // Update existing section
        const updatedData = { ...data };
        const sectionIndex = updatedData.sections.findIndex((s: any) => s.id === editingGallerySection.id);
        if (sectionIndex !== -1) {
          updatedData.sections[sectionIndex] = { ...sectionData, id: editingGallerySection.id };
        }
        onSave(updatedData);
      } else {
        // Add new section
        const response = await fetch('/api/gallery', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(sectionData),
        });
        const result = await response.json();
        if (result.success) {
          // Reload data
          const galleryResponse = await fetch('/api/gallery');
          const galleryData = await galleryResponse.json();
          onSave(galleryData);
        } else {
          alert('Error adding gallery section: ' + result.message);
        }
      }

      setEditingGallerySection(null);
      setShowAddSection(false);
    } catch (e) {
      alert('Invalid JSON format for photos. Please check your input.');
    }
  };

  const deleteGallerySection = async (id: number) => {
    if (!confirm('Are you sure you want to delete this gallery section?')) return;

    try {
      const response = await fetch(`/api/gallery?id=${id}`, {
        method: 'DELETE',
      });
      const result = await response.json();
      if (result.success) {
        // Reload data
        const galleryResponse = await fetch('/api/gallery');
        const galleryData = await galleryResponse.json();
        onSave(galleryData);
      } else {
        alert('Error deleting gallery section: ' + result.message);
      }
    } catch (error) {
      alert('Error deleting gallery section');
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Photo Gallery Management
        </h2>
        <button
          onClick={() => setShowAddSection(true)}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Section
        </button>
      </div>

      {/* Gallery Header */}
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3 style={{ color: 'white', fontSize: '18px', margin: 0 }}>Gallery Header</h3>
          <button
            onClick={startEditingHeader}
            style={{
              padding: '6px 12px',
              background: '#667eea',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            Edit
          </button>
        </div>
        <div style={{ color: 'rgba(255,255,255,0.8)' }}>
          <p><strong>Title:</strong> {data.title}</p>
          <p><strong>Description:</strong> {data.description}</p>
        </div>
      </div>

      {/* Gallery Sections */}
      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {data.sections?.map((section: GallerySection) => (
          <div key={section.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '8px'
                }}>
                  {section.title}
                </h3>
                <div style={{ fontSize: '14px', marginBottom: '8px' }}>
                  <span style={{ color: 'rgba(255,255,255,0.7)' }}>Year: </span>
                  <span style={{ color: '#a5b8ff' }}>{section.year}</span>
                </div>
                <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px', marginBottom: '8px' }}>
                  {section.description}
                </p>
                <p style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px' }}>
                  {section.photos?.length || 0} photos
                </p>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => startEditingGallerySection(section)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '4px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Edit
                </button>
                <button
                  onClick={() => deleteGallerySection(section.id)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(239, 68, 68, 0.2)',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '4px',
                    color: '#fca5a5',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Edit Modals */}
      {editingSection === 'header' && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '16px',
            padding: '32px',
            maxWidth: '600px',
            width: '100%'
          }}>
            <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
              Edit Gallery Header
            </h2>

            <form onSubmit={(e) => { e.preventDefault(); saveHeader(); }} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  style={{
                    width: '100%',
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
              </div>
              <div>
                <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    color: 'white',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
                <button
                  type="submit"
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  Save Changes
                </button>
                <button
                  type="button"
                  onClick={() => setEditingSection(null)}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    color: 'white',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {(editingGallerySection || showAddSection) && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'rgba(0,0,0,0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            background: 'rgba(255,255,255,0.1)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255,255,255,0.2)',
            borderRadius: '16px',
            padding: '32px',
            maxWidth: '700px',
            width: '100%',
            maxHeight: '90vh',
            overflowY: 'auto'
          }}>
            <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
              {editingGallerySection ? 'Edit Gallery Section' : 'Add New Gallery Section'}
            </h2>

            <form onSubmit={(e) => { e.preventDefault(); saveGallerySection(); }} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
              <div>
                <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  required
                  style={{
                    width: '100%',
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    color: 'white'
                  }}
                />
              </div>

              <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
                <div>
                  <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Year</label>
                  <input
                    type="text"
                    value={formData.year}
                    onChange={(e) => setFormData({ ...formData, year: e.target.value })}
                    required
                    style={{
                      width: '100%',
                      padding: '12px',
                      background: 'rgba(255,255,255,0.1)',
                      border: '1px solid rgba(255,255,255,0.2)',
                      borderRadius: '8px',
                      color: 'white'
                    }}
                  />
                </div>
              </div>

              <div>
                <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  required
                  rows={3}
                  style={{
                    width: '100%',
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    color: 'white',
                    resize: 'vertical'
                  }}
                />
              </div>

              <div>
                <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Photos JSON</label>
                <textarea
                  value={formData.photos}
                  onChange={(e) => setFormData({ ...formData, photos: e.target.value })}
                  rows={10}
                  placeholder='[{"url": "https://example.com/photo.jpg", "caption": "Photo caption", "alt": "Alt text"}]'
                  style={{
                    width: '100%',
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    color: 'white',
                    fontFamily: 'monospace',
                    fontSize: '14px',
                    resize: 'vertical'
                  }}
                />
                <p style={{ color: 'rgba(255,255,255,0.6)', fontSize: '12px', marginTop: '8px' }}>
                  Format: Array of objects with url, caption, and alt properties
                </p>
              </div>

              <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
                <button
                  type="submit"
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                    color: 'white',
                    border: 'none',
                    borderRadius: '8px',
                    fontWeight: '600',
                    cursor: 'pointer'
                  }}
                >
                  {editingGallerySection ? 'Update' : 'Add'} Section
                </button>
                <button
                  type="button"
                  onClick={() => {
                    setEditingGallerySection(null);
                    setShowAddSection(false);
                  }}
                  style={{
                    flex: 1,
                    padding: '12px',
                    background: 'rgba(255,255,255,0.1)',
                    color: 'white',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '8px',
                    cursor: 'pointer'
                  }}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminPanel;
