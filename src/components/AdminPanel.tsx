import React, { useState, useEffect } from 'react';

interface Member {
  id: number;
  name: string;
  position: string;
  specialty: string;
  institution: string;
  bio: string;
  email: string;
  image: string;
}

interface Project {
  id: number;
  title: string;
  location: string;
  status: string;
  leadInvestigator: string;
  startDate: string;
  endDate: string;
  description: string;
  participants: number;
  budget: string;
  outcomes: string;
}

interface Event {
  id: number;
  title: string;
  date: string;
  time: string;
  location: string;
  description: string;
  registrationLink: string;
}

const AdminPanel: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('content');
  const [members, setMembers] = useState<Member[]>([]);
  const [projects, setProjects] = useState<Project[]>([]);
  const [events, setEvents] = useState<Event[]>([]);
  const [siteContent, setSiteContent] = useState<any>({});

  // Authentication state
  const [loginForm, setLoginForm] = useState({ username: '', password: '' });
  const [loginError, setLoginError] = useState('');

  // Editing states
  const [editingMember, setEditingMember] = useState<Member | null>(null);
  const [editingProject, setEditingProject] = useState<Project | null>(null);
  const [editingEvent, setEditingEvent] = useState<Event | null>(null);
  const [showAddMember, setShowAddMember] = useState(false);
  const [showAddProject, setShowAddProject] = useState(false);
  const [showAddEvent, setShowAddEvent] = useState(false);

  // Check authentication status on component mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const response = await fetch('/api/auth');
      const data = await response.json();
      setIsAuthenticated(data.authenticated);
    } catch (error) {
      console.error('Auth check failed:', error);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginError('');
    
    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginForm),
      });
      
      const data = await response.json();
      
      if (data.success) {
        setIsAuthenticated(true);
        loadData();
      } else {
        setLoginError(data.message);
      }
    } catch (error) {
      setLoginError('Login failed. Please try again.');
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth', { method: 'DELETE' });
      setIsAuthenticated(false);
      setLoginForm({ username: '', password: '' });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const loadData = async () => {
    try {
      // Load members
      const membersResponse = await fetch('/api/members');
      const membersData = await membersResponse.json();
      setMembers(membersData.boardMembers || []);

      // Load projects
      const projectsResponse = await fetch('/api/projects');
      const projectsData = await projectsResponse.json();
      setProjects(projectsData.globalHealthProjects || []);

      // Load content
      const contentResponse = await fetch('/api/content');
      const contentData = await contentResponse.json();
      setSiteContent(contentData);
      setEvents(contentData.site?.upcomingEvents || []);
    } catch (error) {
      console.error('Failed to load data:', error);
    }
  };

  useEffect(() => {
    if (isAuthenticated) {
      loadData();
    }
  }, [isAuthenticated]);

  // CRUD Functions for Members
  const saveMember = async (member: Partial<Member>) => {
    try {
      const method = member.id ? 'PUT' : 'POST';
      const response = await fetch('/api/members', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(member),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        setEditingMember(null);
        setShowAddMember(false);
      } else {
        alert('Error saving member: ' + data.message);
      }
    } catch (error) {
      alert('Error saving member');
    }
  };

  const deleteMember = async (id: number) => {
    if (!confirm('Are you sure you want to delete this member?')) return;

    try {
      const response = await fetch(`/api/members?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
      } else {
        alert('Error deleting member: ' + data.message);
      }
    } catch (error) {
      alert('Error deleting member');
    }
  };

  // CRUD Functions for Projects
  const saveProject = async (project: Partial<Project>) => {
    try {
      const method = project.id ? 'PUT' : 'POST';
      const response = await fetch('/api/projects', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(project),
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
        setEditingProject(null);
        setShowAddProject(false);
      } else {
        alert('Error saving project: ' + data.message);
      }
    } catch (error) {
      alert('Error saving project');
    }
  };

  const deleteProject = async (id: number) => {
    if (!confirm('Are you sure you want to delete this project?')) return;

    try {
      const response = await fetch(`/api/projects?id=${id}`, {
        method: 'DELETE',
      });

      const data = await response.json();
      if (data.success) {
        await loadData(); // Reload data
      } else {
        alert('Error deleting project: ' + data.message);
      }
    } catch (error) {
      alert('Error deleting project');
    }
  };



  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="glass-card p-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto"></div>
          <p className="text-white mt-4">Loading...</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return (
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '80vh',
        padding: '20px'
      }}>
        <div style={{
          background: 'rgba(255,255,255,0.1)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255,255,255,0.2)',
          borderRadius: '16px',
          padding: '32px',
          maxWidth: '400px',
          width: '100%',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
        }}>
          <h2 style={{
            fontSize: '24px',
            fontWeight: 'bold',
            color: 'white',
            marginBottom: '24px',
            textAlign: 'center'
          }}>
            Admin Login
          </h2>
          <form onSubmit={handleLogin} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
            <div>
              <label style={{
                color: 'white',
                fontWeight: '500',
                marginBottom: '8px',
                display: 'block'
              }}>
                Username
              </label>
              <input
                type="text"
                value={loginForm.username}
                onChange={(e) => setLoginForm({ ...loginForm, username: e.target.value })}
                placeholder="Enter username"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '16px'
                }}
              />
            </div>
            <div>
              <label style={{
                color: 'white',
                fontWeight: '500',
                marginBottom: '8px',
                display: 'block'
              }}>
                Password
              </label>
              <input
                type="password"
                value={loginForm.password}
                onChange={(e) => setLoginForm({ ...loginForm, password: e.target.value })}
                placeholder="Enter password"
                required
                style={{
                  width: '100%',
                  padding: '12px 16px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white',
                  fontSize: '16px'
                }}
              />
            </div>
            {loginError && (
              <div style={{
                background: 'rgba(239, 68, 68, 0.2)',
                color: '#fca5a5',
                padding: '12px',
                borderRadius: '8px',
                fontSize: '14px'
              }}>
                {loginError}
              </div>
            )}
            <button
              type="submit"
              style={{
                width: '100%',
                padding: '12px 24px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '25px',
                fontWeight: '600',
                fontSize: '16px',
                cursor: 'pointer',
                boxShadow: '0 10px 25px rgba(0, 0, 0, 0.3)',
                transition: 'transform 0.2s'
              }}
              onMouseOver={(e) => e.currentTarget.style.transform = 'scale(1.05)'}
              onMouseOut={(e) => e.currentTarget.style.transform = 'scale(1)'}
            >
              Login
            </button>
          </form>
          <div style={{
            marginTop: '24px',
            padding: '16px',
            background: 'rgba(255,255,255,0.05)',
            borderRadius: '8px'
          }}>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '14px',
              textAlign: 'center',
              lineHeight: '1.5'
            }}>
              Demo Credentials:<br />
              Username: <span style={{ color: 'white', fontFamily: 'monospace' }}>admin</span><br />
              Password: <span style={{ color: 'white', fontFamily: 'monospace' }}>password</span>
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '32px 16px' }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        marginBottom: '32px',
        boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)'
      }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: '24px',
          flexWrap: 'wrap',
          gap: '16px'
        }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: 'bold',
            color: 'white',
            margin: 0
          }}>
            Admin Panel
          </h1>
          <button
            onClick={handleLogout}
            style={{
              padding: '8px 16px',
              background: 'rgba(255,255,255,0.1)',
              border: '1px solid rgba(255,255,255,0.2)',
              borderRadius: '20px',
              color: 'white',
              cursor: 'pointer',
              fontSize: '14px',
              fontWeight: '500'
            }}
          >
            Logout
          </button>
        </div>

        {/* Tab Navigation */}
        <div style={{
          display: 'flex',
          gap: '16px',
          marginBottom: '32px',
          overflowX: 'auto',
          paddingBottom: '8px'
        }}>
          {[
            { id: 'content', label: 'Site Content' },
            { id: 'members', label: 'Board Members' },
            { id: 'projects', label: 'Global Health Projects' },
            { id: 'events', label: 'Events' }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              style={{
                padding: '8px 16px',
                borderRadius: '8px',
                fontWeight: '500',
                whiteSpace: 'nowrap',
                border: 'none',
                cursor: 'pointer',
                transition: 'all 0.2s',
                background: activeTab === tab.id ? '#667eea' : 'transparent',
                color: activeTab === tab.id ? 'white' : 'rgba(255,255,255,0.7)'
              }}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div style={{ minHeight: '400px' }}>
          {activeTab === 'content' && <ContentManager content={siteContent} />}
          {activeTab === 'members' && (
            <MembersManager
              members={members}
              onEdit={setEditingMember}
              onDelete={deleteMember}
              onAdd={() => setShowAddMember(true)}
            />
          )}
          {activeTab === 'projects' && (
            <ProjectsManager
              projects={projects}
              onEdit={setEditingProject}
              onDelete={deleteProject}
              onAdd={() => setShowAddProject(true)}
            />
          )}
          {activeTab === 'events' && (
            <EventsManager
              events={events}
              onEdit={setEditingEvent}
              onAdd={() => setShowAddEvent(true)}
            />
          )}
        </div>

        {/* Modal Forms */}
        {(editingMember || showAddMember) && (
          <MemberForm
            member={editingMember}
            onSave={saveMember}
            onCancel={() => {
              setEditingMember(null);
              setShowAddMember(false);
            }}
          />
        )}

        {(editingProject || showAddProject) && (
          <ProjectForm
            project={editingProject}
            onSave={saveProject}
            onCancel={() => {
              setEditingProject(null);
              setShowAddProject(false);
            }}
          />
        )}
      </div>
    </div>
  );
};

// Content Manager Component
const ContentManager: React.FC<{ content: any }> = ({ content }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
        Site Content Management
      </h2>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        borderRadius: '8px',
        padding: '24px',
        border: '1px solid rgba(255,255,255,0.1)'
      }}>
        <p style={{ color: 'rgba(255,255,255,0.8)', marginBottom: '16px' }}>
          Content management features will be implemented here. This includes:
        </p>
        <ul style={{
          color: 'rgba(255,255,255,0.7)',
          paddingLeft: '20px',
          lineHeight: '1.6'
        }}>
          <li style={{ marginBottom: '8px' }}>Edit hero section content</li>
          <li style={{ marginBottom: '8px' }}>Update organization description</li>
          <li style={{ marginBottom: '8px' }}>Manage navigation links</li>
          <li style={{ marginBottom: '8px' }}>Update admin resources</li>
        </ul>
      </div>
    </div>
  );
};

// Members Manager Component
const MembersManager: React.FC<{
  members: Member[];
  onEdit: (member: Member) => void;
  onDelete: (id: number) => void;
  onAdd: () => void;
}> = ({ members, onEdit, onDelete, onAdd }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Board Members Management
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Member
        </button>
      </div>

      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        gap: '24px'
      }}>
        {members.map((member) => (
          <div key={member.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <h3 style={{
              fontSize: '18px',
              fontWeight: 'bold',
              color: 'white',
              marginBottom: '8px'
            }}>
              {member.name}
            </h3>
            <p style={{
              color: '#a5b8ff',
              fontSize: '14px',
              marginBottom: '8px'
            }}>
              {member.position}
            </p>
            <p style={{
              color: 'rgba(255,255,255,0.7)',
              fontSize: '14px',
              marginBottom: '8px'
            }}>
              {member.specialty}
            </p>
            <p style={{
              color: 'rgba(255,255,255,0.6)',
              fontSize: '14px',
              marginBottom: '16px'
            }}>
              {member.institution}
            </p>
            <div style={{ display: 'flex', gap: '8px' }}>
              <button
                onClick={() => onEdit(member)}
                style={{
                  padding: '4px 12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '4px',
                  color: 'white',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                Edit
              </button>
              <button
                onClick={() => onDelete(member.id)}
                style={{
                  padding: '4px 12px',
                  background: 'rgba(239, 68, 68, 0.2)',
                  border: '1px solid rgba(239, 68, 68, 0.3)',
                  borderRadius: '4px',
                  color: '#fca5a5',
                  cursor: 'pointer',
                  fontSize: '12px'
                }}
              >
                Delete
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Projects Manager Component
const ProjectsManager: React.FC<{
  projects: Project[];
  onEdit: (project: Project) => void;
  onDelete: (id: number) => void;
  onAdd: () => void;
}> = ({ projects, onEdit, onDelete, onAdd }) => {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active': return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
      case 'planning': return { bg: 'rgba(234, 179, 8, 0.2)', color: '#fde047' };
      case 'completed': return { bg: 'rgba(59, 130, 246, 0.2)', color: '#93c5fd' };
      default: return { bg: 'rgba(34, 197, 94, 0.2)', color: '#86efac' };
    }
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Global Health Projects
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Project
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {projects.map((project) => (
          <div key={project.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {project.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                  gap: '16px',
                  fontSize: '14px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Location</p>
                    <p style={{ color: 'white' }}>{project.location}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Status</p>
                    <span style={{
                      ...getStatusColor(project.status),
                      padding: '2px 8px',
                      borderRadius: '12px',
                      fontSize: '12px',
                      fontWeight: '500'
                    }}>
                      {project.status}
                    </span>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Lead</p>
                    <p style={{ color: 'white' }}>{project.leadInvestigator}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Budget</p>
                    <p style={{ color: '#a5b8ff' }}>{project.budget}</p>
                  </div>
                </div>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => onEdit(project)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '4px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Edit
                </button>
                <button
                  onClick={() => onDelete(project.id)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(239, 68, 68, 0.2)',
                    border: '1px solid rgba(239, 68, 68, 0.3)',
                    borderRadius: '4px',
                    color: '#fca5a5',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Events Manager Component
const EventsManager: React.FC<{
  events: Event[];
  onEdit: (event: Event) => void;
  onAdd: () => void;
}> = ({ events, onEdit, onAdd }) => {
  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        flexWrap: 'wrap',
        gap: '16px'
      }}>
        <h2 style={{ fontSize: '20px', fontWeight: 'bold', color: 'white', margin: 0 }}>
          Upcoming Events
        </h2>
        <button
          onClick={onAdd}
          style={{
            padding: '8px 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            color: 'white',
            border: 'none',
            borderRadius: '20px',
            fontWeight: '600',
            cursor: 'pointer',
            fontSize: '14px'
          }}
        >
          Add New Event
        </button>
      </div>

      <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
        {events.map((event) => (
          <div key={event.id} style={{
            background: 'rgba(255,255,255,0.1)',
            borderRadius: '8px',
            padding: '16px',
            border: '1px solid rgba(255,255,255,0.1)'
          }}>
            <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', gap: '16px' }}>
              <div style={{ flex: 1 }}>
                <h3 style={{
                  fontSize: '18px',
                  fontWeight: 'bold',
                  color: 'white',
                  marginBottom: '12px'
                }}>
                  {event.title}
                </h3>
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',
                  gap: '16px',
                  fontSize: '14px',
                  marginBottom: '12px'
                }}>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Date & Time</p>
                    <p style={{ color: 'white' }}>
                      {new Date(event.date).toLocaleDateString()} at {event.time}
                    </p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Location</p>
                    <p style={{ color: 'white' }}>{event.location}</p>
                  </div>
                  <div>
                    <p style={{ color: 'rgba(255,255,255,0.7)', marginBottom: '4px' }}>Registration</p>
                    <a
                      href={event.registrationLink}
                      style={{ color: '#a5b8ff', textDecoration: 'none' }}
                      onMouseOver={(e) => e.currentTarget.style.color = '#c7d6ff'}
                      onMouseOut={(e) => e.currentTarget.style.color = '#a5b8ff'}
                    >
                      View Link
                    </a>
                  </div>
                </div>
                <p style={{ color: 'rgba(255,255,255,0.8)', fontSize: '14px' }}>
                  {event.description}
                </p>
              </div>
              <div style={{ display: 'flex', gap: '8px' }}>
                <button
                  onClick={() => onEdit(event)}
                  style={{
                    padding: '4px 12px',
                    background: 'rgba(255,255,255,0.1)',
                    border: '1px solid rgba(255,255,255,0.2)',
                    borderRadius: '4px',
                    color: 'white',
                    cursor: 'pointer',
                    fontSize: '12px'
                  }}
                >
                  Edit
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// Member Form Component
const MemberForm: React.FC<{
  member: Member | null;
  onSave: (member: Partial<Member>) => void;
  onCancel: () => void;
}> = ({ member, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: member?.name || '',
    position: member?.position || '',
    specialty: member?.specialty || '',
    institution: member?.institution || '',
    bio: member?.bio || '',
    email: member?.email || '',
    image: member?.image || '/images/board/placeholder.jpg'
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(member ? { ...formData, id: member.id } : formData);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '500px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
          {member ? 'Edit Member' : 'Add New Member'}
        </h2>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Position</label>
            <input
              type="text"
              value={formData.position}
              onChange={(e) => setFormData({ ...formData, position: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Specialty</label>
            <input
              type="text"
              value={formData.specialty}
              onChange={(e) => setFormData({ ...formData, specialty: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Institution</label>
            <input
              type="text"
              value={formData.institution}
              onChange={(e) => setFormData({ ...formData, institution: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              required
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Bio</label>
            <textarea
              value={formData.bio}
              onChange={(e) => setFormData({ ...formData, bio: e.target.value })}
              required
              rows={4}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              type="submit"
              style={{
                flex: 1,
                padding: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              {member ? 'Update' : 'Add'} Member
            </button>
            <button
              type="button"
              onClick={onCancel}
              style={{
                flex: 1,
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Project Form Component
const ProjectForm: React.FC<{
  project: Project | null;
  onSave: (project: Partial<Project>) => void;
  onCancel: () => void;
}> = ({ project, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: project?.title || '',
    location: project?.location || '',
    status: project?.status || 'Planning',
    leadInvestigator: project?.leadInvestigator || '',
    startDate: project?.startDate || '',
    endDate: project?.endDate || '',
    description: project?.description || '',
    participants: project?.participants || 0,
    budget: project?.budget || '',
    outcomes: project?.outcomes || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(project ? { ...formData, id: project.id } : formData);
  };

  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      background: 'rgba(0,0,0,0.5)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 1000,
      padding: '20px'
    }}>
      <div style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        border: '1px solid rgba(255,255,255,0.2)',
        borderRadius: '16px',
        padding: '32px',
        maxWidth: '600px',
        width: '100%',
        maxHeight: '90vh',
        overflowY: 'auto'
      }}>
        <h2 style={{ color: 'white', fontSize: '24px', marginBottom: '24px' }}>
          {project ? 'Edit Project' : 'Add New Project'}
        </h2>

        <form onSubmit={handleSubmit} style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Title</label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Location</label>
              <input
                type="text"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Status</label>
              <select
                value={formData.status}
                onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              >
                <option value="Planning">Planning</option>
                <option value="Active">Active</option>
                <option value="Completed">Completed</option>
              </select>
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Lead Investigator</label>
              <input
                type="text"
                value={formData.leadInvestigator}
                onChange={(e) => setFormData({ ...formData, leadInvestigator: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Start Date</label>
              <input
                type="date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>End Date</label>
              <input
                type="date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                required
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '16px' }}>
            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Participants</label>
              <input
                type="number"
                value={formData.participants}
                onChange={(e) => setFormData({ ...formData, participants: parseInt(e.target.value) || 0 })}
                min="0"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>

            <div>
              <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Budget</label>
              <input
                type="text"
                value={formData.budget}
                onChange={(e) => setFormData({ ...formData, budget: e.target.value })}
                placeholder="e.g., $125,000"
                style={{
                  width: '100%',
                  padding: '12px',
                  background: 'rgba(255,255,255,0.1)',
                  border: '1px solid rgba(255,255,255,0.2)',
                  borderRadius: '8px',
                  color: 'white'
                }}
              />
            </div>
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Description</label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              required
              rows={3}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div>
            <label style={{ color: 'white', display: 'block', marginBottom: '8px' }}>Outcomes</label>
            <textarea
              value={formData.outcomes}
              onChange={(e) => setFormData({ ...formData, outcomes: e.target.value })}
              rows={3}
              style={{
                width: '100%',
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                color: 'white',
                resize: 'vertical'
              }}
            />
          </div>

          <div style={{ display: 'flex', gap: '12px', marginTop: '24px' }}>
            <button
              type="submit"
              style={{
                flex: 1,
                padding: '12px',
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontWeight: '600',
                cursor: 'pointer'
              }}
            >
              {project ? 'Update' : 'Add'} Project
            </button>
            <button
              type="button"
              onClick={onCancel}
              style={{
                flex: 1,
                padding: '12px',
                background: 'rgba(255,255,255,0.1)',
                color: 'white',
                border: '1px solid rgba(255,255,255,0.2)',
                borderRadius: '8px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AdminPanel;
