---
import Layout from '../components/Layout.astro';
import { readFileSync } from 'fs';
import { join } from 'path';

// Read members data
const membersPath = join(process.cwd(), 'data', 'members.json');
const membersData = JSON.parse(readFileSync(membersPath, 'utf-8'));
const { boardMembers } = membersData;
---

<Layout title="Executive Board - Pediatric POCUS Society">
  <section class="pt-32 pb-16 px-4">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-16">
        <h1 class="section-title">Executive Board</h1>
        <p class="text-xl text-white/90 max-w-3xl mx-auto">
          Meet our distinguished leadership team of pediatric POCUS experts dedicated to advancing 
          point-of-care ultrasound in pediatric medicine worldwide.
        </p>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {boardMembers.map((member: any, index: number) => (
          <div 
            class="glass-card card-hover text-center"
            style={`animation: slideUp 0.6s ease-out ${index * 0.1}s both`}
          >
            <!-- Member Photo Placeholder -->
            <div class="w-32 h-32 bg-gradient-secondary rounded-full mx-auto mb-6 flex items-center justify-center">
              <svg class="w-16 h-16 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            
            <h3 class="text-xl font-bold text-white mb-2">{member.name}</h3>
            <div class="bg-primary-500/20 text-primary-200 px-3 py-1 rounded-full text-sm font-medium mb-3 inline-block">
              {member.position}
            </div>
            <p class="text-white/80 text-sm mb-2">{member.specialty}</p>
            <p class="text-white/70 text-sm mb-4">{member.institution}</p>
            
            <div class="text-left">
              <p class="text-white/90 text-sm leading-relaxed mb-4">{member.bio}</p>
            </div>
            
            <div class="flex items-center justify-center space-x-4 pt-4 border-t border-white/20">
              <a 
                href={`mailto:${member.email}`}
                class="text-primary-300 hover:text-primary-200 transition-colors"
                title="Email"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </a>
              <a 
                href="#"
                class="text-primary-300 hover:text-primary-200 transition-colors"
                title="LinkedIn"
              >
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </a>
            </div>
          </div>
        ))}
      </div>
      
      <!-- Call to Action -->
      <div class="glass-card text-center mt-16">
        <h2 class="text-2xl font-bold text-white mb-4">Join Our Leadership</h2>
        <p class="text-white/90 mb-6 max-w-2xl mx-auto">
          Interested in contributing to the advancement of pediatric POCUS? 
          We welcome applications for committee positions and volunteer opportunities.
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="mailto:<EMAIL>" class="btn-primary">
            Contact Leadership
          </a>
          <a href="/admin" class="btn-secondary">
            View Opportunities
          </a>
        </div>
      </div>
    </div>
  </section>
</Layout>
